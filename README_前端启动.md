# 前端页面启动方式总览

## 🌐 系统信息
- **页面地址**: https://liteweb-test.blacklake.cn/
- **工厂代码**: 990993
- **账号**: admin
- **密码**: Blacklake123

## 🚀 启动方式

### 1. 一键启动脚本（推荐）

#### Windows用户
```cmd
# 双击运行或命令行执行
启动前端.bat
```

#### Mac/Linux用户
```bash
# 命令行执行
./启动前端.sh
```

### 2. Python脚本启动

#### 自动登录（推荐）
```bash
python quick_start.py auto
```

#### 仅打开浏览器
```bash
python quick_start.py open
```

#### 查看帮助
```bash
python quick_start.py help
```

### 3. 通过测试脚本启动
```bash
# 激活虚拟环境（如果使用）
source venv/bin/activate  # Mac/Linux
venv\Scripts\activate     # Windows

# 运行登录测试
python run_tests.py TestCases/test_su_login.py
```

### 4. 直接浏览器访问
1. 打开Chrome浏览器
2. 访问：https://liteweb-test.blacklake.cn/
3. 选择"工厂账号登录"
4. 输入登录信息并登录

### 5. 命令行快速打开
```bash
# 一行命令打开浏览器
python -c "import webbrowser; webbrowser.open('https://liteweb-test.blacklake.cn/')"
```

## 📋 功能模块

登录后可访问：
- **主页** - 系统仪表盘
- **基础数据** - 客户、设备、单位、仓库等管理
- **生产管理** - 工单、生产计划、报工等

## 🔧 环境要求
- Chrome浏览器（推荐）
- Python 3.6+（使用脚本时）
- 稳定的网络连接

## 🛠 故障排除

### 自动登录失败
```bash
# 检查ChromeDriver
python run_tests.py --check-env

# 手动安装ChromeDriver
# Windows: 下载chromedriver.exe到项目目录
# Mac: brew install chromedriver
# Linux: sudo apt install chromium-chromedriver
```

### 网络连接问题
```bash
# 检查网络
ping blacklake.cn

# 检查DNS
nslookup liteweb-test.blacklake.cn
```

### 浏览器兼容性
- 确保使用最新版Chrome
- 清除浏览器缓存
- 尝试无痕模式

## 📞 快速联系

如有问题，请：
1. 查看详细文档：`前端启动指南.md`
2. 检查系统环境：`python run_tests.py --check-env`
3. 联系技术支持

---

**最快启动方式**：
- Windows: 双击 `启动前端.bat`
- Mac/Linux: 运行 `./启动前端.sh`
