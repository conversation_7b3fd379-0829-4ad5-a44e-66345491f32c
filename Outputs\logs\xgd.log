2025-08-14 21:55:14,787 admin INFO conftest.py-11 line：=========class级 前置操作：打开浏览器，访问登录页面=========
2025-08-14 21:55:21,109 admin INFO conftest.py-29 line：=========class级 前置操作：登陆系统=========
2025-08-14 21:55:21,109 admin INFO basepage.py-125 line：在 工厂账号登录 行为，等待元素可点击：('xpath', "//*[@id='rc-tabs-0-tab-1']")
2025-08-14 21:55:21,318 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.21秒
2025-08-14 21:55:21,318 admin INFO basepage.py-161 line：在 登录页面_输入账号名 行为，等待元素可交互：('xpath', '//input[@placeholder="请输入工厂代码"]')
2025-08-14 21:55:21,341 admin INFO basepage.py-169 line：向元素 ('xpath', '//input[@placeholder="请输入工厂代码"]') 输入文本值：990993
2025-08-14 21:55:21,462 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.14秒
2025-08-14 21:55:21,462 admin INFO basepage.py-161 line：在 登录页面_输入用户名 行为，等待元素可交互：('xpath', '//input[@placeholder="请输入账号"]')
2025-08-14 21:55:21,482 admin INFO basepage.py-169 line：向元素 ('xpath', '//input[@placeholder="请输入账号"]') 输入文本值：admin
2025-08-14 21:55:21,563 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.10秒
2025-08-14 21:55:21,563 admin INFO basepage.py-161 line：在 登录页面_输入密码 行为，等待元素可交互：('xpath', '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[3]/div[1]/div/div[1]/div/span/input')
2025-08-14 21:55:21,579 admin INFO basepage.py-169 line：向元素 ('xpath', '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[3]/div[1]/div/div[1]/div/span/input') 输入文本值：Blacklake123
2025-08-14 21:55:21,686 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.12秒
2025-08-14 21:55:21,687 admin INFO basepage.py-125 line：在 登录页面_点击登录按钮 行为，等待元素可点击：('xpath', '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[4]/div/div/div/div/button')
2025-08-14 21:55:21,777 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.09秒
2025-08-14 21:55:21,778 admin INFO basepage.py-125 line：在 点击登录页的跳过按钮 行为，等待元素可点击：('xpath', '//*[@id="root"]/div/div[2]/div[2]/form/div[3]/div/div/div/div/button[1]/span')
2025-08-14 21:55:22,360 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.58秒
2025-08-14 21:55:22,360 admin INFO basepage.py-125 line：在 点击基础数据菜单 行为，等待元素可点击：('xpath', "//span[contains(text(), '基础数据')]")
2025-08-14 21:55:24,161 admin INFO basepage.py-144 line：元素点击成功，总耗时：1.80秒
2025-08-14 21:55:29,162 admin INFO basepage.py-125 line：在 点击单位 行为，等待元素可点击：('xpath', "//a[contains(text(), '单位')]")
2025-08-14 21:55:29,433 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.27秒
2025-08-14 21:55:29,433 admin INFO basepage.py-125 line：在 点击创建单位 行为，等待元素可点击：('xpath', "//span[contains(text(), '创建单位')]")
2025-08-14 21:55:29,764 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.33秒
2025-08-14 21:55:29,764 admin INFO basepage.py-161 line：在 输入单位名称 行为，等待元素可交互：('xpath', '//*[@id="name"]')
2025-08-14 21:55:30,080 admin INFO basepage.py-169 line：向元素 ('xpath', '//*[@id="name"]') 输入文本值：1755179721778_oie
2025-08-14 21:55:30,322 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.56秒
2025-08-14 21:55:30,322 admin INFO basepage.py-161 line：在 点击单位备注 行为，等待元素可交互：('xpath', '//*[@id="remark"]')
2025-08-14 21:55:30,341 admin INFO basepage.py-169 line：向元素 ('xpath', '//*[@id="remark"]') 输入文本值：1755179721778_oie
2025-08-14 21:55:30,474 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.15秒
2025-08-14 21:55:30,475 admin INFO basepage.py-125 line：在  点击确认按钮 行为，等待元素可点击：('xpath', "//span[contains(text(), '确 定')]")
2025-08-14 21:55:30,546 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.07秒
2025-08-14 21:55:30,546 admin INFO basepage.py-209 line：在 创建成功！ 行为，等待元素并获取文本：('xpath', "//span[contains(text(), '创建成功！')]")
2025-08-14 21:55:31,092 admin INFO basepage.py-241 line：成功获取文本[创建成功！]，总耗时：0.55秒
2025-08-14 21:55:31,168 admin INFO conftest.py-16 line：=========class级 后置操作：关闭浏览器，退出会话=========
2025-08-14 21:55:51,441 admin INFO conftest.py-11 line：=========class级 前置操作：打开浏览器，访问登录页面=========
2025-08-14 21:55:54,943 admin INFO conftest.py-29 line：=========class级 前置操作：登陆系统=========
2025-08-14 21:55:54,943 admin INFO basepage.py-125 line：在 工厂账号登录 行为，等待元素可点击：('xpath', "//*[@id='rc-tabs-0-tab-1']")
2025-08-14 21:55:55,138 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.19秒
2025-08-14 21:55:55,138 admin INFO basepage.py-161 line：在 登录页面_输入账号名 行为，等待元素可交互：('xpath', '//input[@placeholder="请输入工厂代码"]')
2025-08-14 21:55:55,157 admin INFO basepage.py-169 line：向元素 ('xpath', '//input[@placeholder="请输入工厂代码"]') 输入文本值：990993
2025-08-14 21:55:55,256 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.12秒
2025-08-14 21:55:55,256 admin INFO basepage.py-161 line：在 登录页面_输入用户名 行为，等待元素可交互：('xpath', '//input[@placeholder="请输入账号"]')
2025-08-14 21:55:55,274 admin INFO basepage.py-169 line：向元素 ('xpath', '//input[@placeholder="请输入账号"]') 输入文本值：admin
2025-08-14 21:55:55,338 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.08秒
2025-08-14 21:55:55,338 admin INFO basepage.py-161 line：在 登录页面_输入密码 行为，等待元素可交互：('xpath', '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[3]/div[1]/div/div[1]/div/span/input')
2025-08-14 21:55:55,354 admin INFO basepage.py-169 line：向元素 ('xpath', '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[3]/div[1]/div/div[1]/div/span/input') 输入文本值：Blacklake123
2025-08-14 21:55:55,451 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.11秒
2025-08-14 21:55:55,451 admin INFO basepage.py-125 line：在 登录页面_点击登录按钮 行为，等待元素可点击：('xpath', '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[4]/div/div/div/div/button')
2025-08-14 21:55:55,524 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.07秒
2025-08-14 21:55:55,525 admin INFO basepage.py-125 line：在 点击登录页的跳过按钮 行为，等待元素可点击：('xpath', '//*[@id="root"]/div/div[2]/div[2]/form/div[3]/div/div/div/div/button[1]/span')
2025-08-14 21:55:56,089 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.56秒
2025-08-14 21:55:56,089 admin INFO basepage.py-125 line：在 点击基础数据菜单 行为，等待元素可点击：('xpath', "//span[contains(text(), '基础数据')]")
2025-08-14 21:55:57,841 admin INFO basepage.py-144 line：元素点击成功，总耗时：1.75秒
2025-08-14 21:56:02,841 admin INFO basepage.py-125 line：在 点击单位 行为，等待元素可点击：('xpath', "//a[contains(text(), '单位')]")
2025-08-14 21:56:03,100 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.26秒
2025-08-14 21:56:03,101 admin INFO basepage.py-125 line：在 点击创建单位 行为，等待元素可点击：('xpath', "//span[contains(text(), '创建单位')]")
2025-08-14 21:56:03,274 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.17秒
2025-08-14 21:56:03,274 admin INFO basepage.py-161 line：在 输入单位名称 行为，等待元素可交互：('xpath', '//*[@id="name"]')
2025-08-14 21:56:03,914 admin INFO basepage.py-169 line：向元素 ('xpath', '//*[@id="name"]') 输入文本值：1755179755525_bph
2025-08-14 21:56:04,071 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.80秒
2025-08-14 21:56:04,071 admin INFO basepage.py-161 line：在 点击单位备注 行为，等待元素可交互：('xpath', '//*[@id="remark"]')
2025-08-14 21:56:04,108 admin INFO basepage.py-169 line：向元素 ('xpath', '//*[@id="remark"]') 输入文本值：1755179755525_bph
2025-08-14 21:56:04,244 admin INFO basepage.py-182 line：文本输入成功，总耗时：0.17秒
2025-08-14 21:56:04,244 admin INFO basepage.py-125 line：在  点击确认按钮 行为，等待元素可点击：('xpath', "//span[contains(text(), '确 定')]")
2025-08-14 21:56:04,324 admin INFO basepage.py-144 line：元素点击成功，总耗时：0.08秒
2025-08-14 21:56:04,324 admin INFO basepage.py-209 line：在 创建成功！ 行为，等待元素并获取文本：('xpath', "//span[contains(text(), '创建成功！')]")
2025-08-14 21:56:04,930 admin INFO basepage.py-241 line：成功获取文本[创建成功！]，总耗时：0.61秒
2025-08-14 21:56:04,931 admin INFO conftest.py-16 line：=========class级 后置操作：关闭浏览器，退出会话=========
