import os.path
import sys
import platform
import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from TestDatas import global_datas as g_data
from PageObjects.login_page import LoginPage
from Common.my_logger import logger
from Common.handle_path import conf_dir

def get_chrome_driver():
    """
    跨平台获取ChromeDriver实例
    """
    chrome_options = Options()

    # 检测操作系统
    current_platform = platform.system().lower()
    logger.info(f"当前操作系统: {current_platform}")

    # 根据平台设置ChromeDriver路径
    if current_platform == 'darwin':  # Mac
        # Mac上可能需要使用系统安装的chromedriver或指定路径
        try:
            # 首先尝试使用系统PATH中的chromedriver
            driver = webdriver.Chrome(options=chrome_options)
            logger.info("使用系统PATH中的ChromeDriver (Mac)")
            return driver
        except Exception as e:
            logger.warning(f"无法使用系统ChromeDriver: {e}")
            # 如果有Mac版本的chromedriver，可以在这里指定路径
            # service = Service('/path/to/chromedriver')  # 需要替换为实际路径
            # driver = webdriver.Chrome(service=service, options=chrome_options)
            raise Exception("请确保已安装ChromeDriver或设置正确的路径")

    elif current_platform == 'windows':  # Windows
        # Windows使用项目中的chromedriver.exe
        chromedriver_path = os.path.join(conf_dir, 'chromedriver.exe')
        if not os.path.exists(chromedriver_path):
            # 尝试使用根目录的chromedriver.exe
            chromedriver_path = os.path.join(os.path.dirname(conf_dir), 'chromedriver.exe')

        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info(f"使用项目ChromeDriver: {chromedriver_path}")
            return driver
        else:
            # 尝试使用系统PATH中的chromedriver
            driver = webdriver.Chrome(options=chrome_options)
            logger.info("使用系统PATH中的ChromeDriver (Windows)")
            return driver

    else:  # Linux或其他系统
        try:
            driver = webdriver.Chrome(options=chrome_options)
            logger.info("使用系统PATH中的ChromeDriver (Linux/Other)")
            return driver
        except Exception as e:
            logger.error(f"无法启动ChromeDriver: {e}")
            raise

#定义fixture - 前置后置 -作用域 -返回值
@pytest.fixture(scope="class")
def init():
    #实例化driver，访问登录页面
    logger.info("=========class级 前置操作：打开浏览器，访问登录页面=========")
    driver = get_chrome_driver()
    driver.maximize_window()
    driver.get(g_data.login_url)
    yield driver
    logger.info("=========class级 后置操作：关闭浏览器，退出会话=========")
    driver.quit()

# 前置条件：用户已登陆   后置条件：关闭浏览器
@pytest.fixture
def back_login(init):
    init.get(g_data.login_url)
    yield init


# 前置条件：用户已登陆   后置条件：关闭浏览器
@pytest.fixture(scope="class")
def access_base(init):
    logger.info("=========class级 前置操作：登陆系统=========")
    LoginPage(init).login(*g_data.account_user)
    yield init


