# 核心测试框架
pytest>=6.2.4
selenium>=4.0.0

# 报告生成
allure-pytest>=2.9.0
pytest-html>=3.1.0

# 测试增强
pytest-xdist>=2.5.0          # 并行执行
pytest-timeout>=2.1.0        # 超时控制
pytest-rerunfailures>=10.2   # 失败重试
pytest-mock>=3.6.0           # Mock支持

# 数据处理
openpyxl>=3.0.0              # Excel文件处理
pandas>=1.3.0                # 数据分析（可选）

# 日志和配置
configparser>=5.0.0          # 配置文件解析

# 开发工具（可选）
pytest-cov>=3.0.0           # 代码覆盖率
pytest-benchmark>=3.4.0     # 性能测试
