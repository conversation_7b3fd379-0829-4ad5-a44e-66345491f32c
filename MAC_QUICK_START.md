# Mac平台快速开始指南

## 🚀 快速解决方案

### 1. 一键环境设置
```bash
# 在项目根目录运行
./setup_mac.sh
```

### 2. 激活环境并测试
```bash
# 激活虚拟环境
source venv/bin/activate

# 检查环境
python run_tests.py --check-env

# 运行单个测试
python run_tests.py TestCases/test_su_login.py

# 运行多个测试（解决多文件选择问题）
python run_tests.py TestCases/test_su_login.py TestCases/test_warehouse.py

# 运行所有测试
python run_tests.py
```

## 🔧 如果遇到ChromeDriver权限问题

```bash
# 解除Mac的安全限制
xattr -d com.apple.quarantine $(which chromedriver)
```

## 📝 主要改进

1. **跨平台兼容的WebDriver配置** - 自动检测Mac系统并使用正确的ChromeDriver
2. **新的运行脚本** - `run_tests.py` 支持多文件执行
3. **pytest配置文件** - `pytest.ini` 包含最佳实践配置
4. **自动化环境设置** - `setup_mac.sh` 一键安装所有依赖

## ✅ 验证解决方案

运行以下命令验证多文件执行是否正常：

```bash
# 测试多文件执行
python run_tests.py TestCases/test_*.py -v

# 应该能看到所有测试文件都被执行
```

## 🆘 如果还有问题

1. 确保在项目根目录 (`BlackLake/`) 运行命令
2. 确保虚拟环境已激活 (`source venv/bin/activate`)
3. 检查Chrome浏览器是否已安装
4. 运行 `python run_tests.py --check-env` 检查环境配置
