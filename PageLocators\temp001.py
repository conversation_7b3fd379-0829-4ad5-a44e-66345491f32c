from selenium.webdriver.common.by import By

# 基础数据--->单位
class UnitPageLocs:
    # 点击单位
    unit_loc = (By.XPATH, "//a[contains(text(), '单位')]")

    # 创建单位
    create_unit_loc = (By.XPATH, "//span[contains(text(), '创建单位')]")
    # 单位名称
    input_unit_name_loc= (By.XPATH, '//*[@id="name"]')
    # 单位备注
    input_unit_remark_loc= (By.XPATH, '//*[@id="remark"]')
    # 点击确认按钮
    unit_submit_loc= (By.XPATH, "//span[contains(text(), '确 定')]")
    # 创建单位成功提示
    create_tip_loc = (By.XPATH, "//span[contains(text(), '创建成功！')]")