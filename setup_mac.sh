#!/bin/bash
# Mac平台环境设置脚本

echo "🍎 Mac平台自动化测试环境设置"
echo "================================"

# 检查是否为Mac系统
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ 此脚本仅适用于Mac系统"
    exit 1
fi

# 检查Homebrew
if ! command -v brew &> /dev/null; then
    echo "📦 安装Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
else
    echo "✓ Homebrew已安装"
fi

# 检查Chrome浏览器
if [ ! -d "/Applications/Google Chrome.app" ]; then
    echo "⚠ 请先安装Google Chrome浏览器"
    echo "下载地址: https://www.google.com/chrome/"
    read -p "按回车键继续..."
fi

# 安装ChromeDriver
echo "🚗 安装ChromeDriver..."
if command -v chromedriver &> /dev/null; then
    echo "✓ ChromeDriver已安装"
    chromedriver --version
else
    brew install chromedriver
    echo "✓ ChromeDriver安装完成"
fi

# 检查Python
echo "🐍 检查Python环境..."
if command -v python3 &> /dev/null; then
    echo "✓ Python3已安装: $(python3 --version)"
else
    echo "📦 安装Python3..."
    brew install python3
fi

# 检查pip
if command -v pip3 &> /dev/null; then
    echo "✓ pip3已安装"
else
    echo "📦 安装pip3..."
    python3 -m ensurepip --upgrade
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境并安装依赖
echo "📦 安装Python依赖..."
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装基础依赖
pip install pytest selenium allure-pytest pytest-html

# 安装可选依赖
pip install pytest-xdist pytest-timeout pytest-rerunfailures

echo ""
echo "✅ Mac环境设置完成！"
echo ""
echo "使用方法："
echo "1. 激活虚拟环境: source venv/bin/activate"
echo "2. 运行单个测试: python run_tests.py TestCases/test_su_login.py"
echo "3. 运行多个测试: python run_tests.py TestCases/test_*.py"
echo "4. 运行所有测试: python run_tests.py"
echo "5. 检查环境: python run_tests.py --check-env"
echo ""
echo "注意事项："
echo "- 首次运行ChromeDriver时，Mac可能会提示安全警告"
echo "- 请在系统偏好设置 > 安全性与隐私中允许ChromeDriver运行"
echo "- 或者运行: xattr -d com.apple.quarantine /usr/local/bin/chromedriver"
