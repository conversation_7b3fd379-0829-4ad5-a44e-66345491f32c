# Mac平台自动化测试使用指南

## 问题描述
在Mac平台上，选择多个测试文件时无法正常驱动执行，只能执行单个文件。

## 解决方案

### 1. 环境设置

#### 自动设置（推荐）
```bash
# 运行Mac环境设置脚本
./setup_mac.sh
```

#### 手动设置
```bash
# 1. 安装Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 2. 安装ChromeDriver
brew install chromedriver

# 3. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt
```

### 2. 解决ChromeDriver权限问题

Mac系统可能会阻止ChromeDriver运行，需要执行以下命令：

```bash
# 方法1：移除隔离属性
xattr -d com.apple.quarantine $(which chromedriver)

# 方法2：或者在系统偏好设置中允许
# 系统偏好设置 > 安全性与隐私 > 通用 > 允许从以下位置下载的应用
```

### 3. 使用新的运行方式

#### 使用跨平台运行脚本（推荐）

```bash
# 激活虚拟环境
source venv/bin/activate

# 运行单个测试文件
python run_tests.py TestCases/test_su_login.py

# 运行多个测试文件
python run_tests.py TestCases/test_su_login.py TestCases/test_warehouse.py

# 运行所有测试
python run_tests.py

# 运行特定标记的测试
python run_tests.py -m smoke

# 运行包含关键字的测试
python run_tests.py -k login

# 并行执行测试
python run_tests.py --parallel

# 生成HTML报告
python run_tests.py --html-report ./reports/report.html

# 生成Allure报告
python run_tests.py --allure

# 检查环境配置
python run_tests.py --check-env
```

#### 直接使用pytest

```bash
# 运行单个文件
pytest TestCases/test_su_login.py -v

# 运行多个文件
pytest TestCases/test_su_login.py TestCases/test_warehouse.py -v

# 运行整个目录
pytest TestCases/ -v

# 使用配置文件运行
pytest -c pytest.ini
```

### 4. IDE配置（PyCharm）

#### 配置测试运行器
1. 打开 PyCharm > Preferences > Tools > Python Integrated Tools
2. 设置 Default test runner 为 pytest
3. 确保 Project interpreter 指向正确的虚拟环境

#### 多文件执行配置
1. 选择多个测试文件
2. 右键 > Run 'pytest in ...'
3. 或者创建自定义运行配置：
   - Run/Debug Configurations > + > Python tests > pytest
   - Target: Custom
   - Additional Arguments: 添加需要的参数

### 5. 常见问题解决

#### 问题1：ChromeDriver权限被拒绝
```bash
# 解决方案
xattr -d com.apple.quarantine $(which chromedriver)
```

#### 问题2：找不到ChromeDriver
```bash
# 检查ChromeDriver是否在PATH中
which chromedriver

# 如果没有，重新安装
brew reinstall chromedriver
```

#### 问题3：Python模块导入错误
```bash
# 确保在项目根目录运行测试
cd /path/to/BlackLake
export PYTHONPATH=$PWD:$PYTHONPATH
```

#### 问题4：多文件执行失败
使用新的运行脚本而不是直接在IDE中选择多文件：
```bash
python run_tests.py TestCases/test_*.py
```

### 6. 性能优化

#### 并行执行
```bash
# 安装pytest-xdist
pip install pytest-xdist

# 并行运行
python run_tests.py --parallel
# 或
pytest -n auto
```

#### 失败重试
```bash
# 安装pytest-rerunfailures
pip install pytest-rerunfailures

# 失败重试
pytest --reruns 2 --reruns-delay 1
```

### 7. 报告生成

#### HTML报告
```bash
python run_tests.py --html-report ./reports/report.html
```

#### Allure报告
```bash
# 生成测试数据
python run_tests.py --allure

# 生成并查看报告
allure serve allure-results
```

## 文件说明

- `pytest.ini`: pytest配置文件，包含跨平台兼容设置
- `run_tests.py`: 跨平台测试运行脚本
- `setup_mac.sh`: Mac环境自动设置脚本
- `TestCases/conftest.py`: 已更新为跨平台兼容的fixture配置

## 注意事项

1. **虚拟环境**：建议使用虚拟环境隔离依赖
2. **权限问题**：Mac首次运行ChromeDriver需要处理安全权限
3. **路径问题**：确保在项目根目录运行测试
4. **依赖版本**：保持selenium和pytest版本兼容

## 技术支持

如果仍有问题，请检查：
1. Python版本（建议3.8+）
2. Chrome浏览器版本与ChromeDriver版本匹配
3. 虚拟环境是否正确激活
4. 项目路径是否正确
