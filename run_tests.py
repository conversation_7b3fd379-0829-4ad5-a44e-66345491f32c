#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台测试运行脚本
支持单文件、多文件和目录执行
"""

import os
import sys
import platform
import argparse
import subprocess
from pathlib import Path


def get_platform_info():
    """获取平台信息"""
    system = platform.system().lower()
    return {
        'system': system,
        'is_mac': system == 'darwin',
        'is_windows': system == 'windows',
        'is_linux': system == 'linux'
    }


def check_chromedriver():
    """检查ChromeDriver是否可用"""
    platform_info = get_platform_info()
    
    if platform_info['is_windows']:
        # Windows检查
        chromedriver_paths = [
            './chromedriver.exe',
            './Conf/chromedriver.exe',
            'chromedriver.exe'
        ]
        
        for path in chromedriver_paths:
            if os.path.exists(path):
                print(f"✓ 找到ChromeDriver: {path}")
                return True
    
    # Mac/Linux检查系统PATH
    try:
        result = subprocess.run(['which', 'chromedriver'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ 找到ChromeDriver: {result.stdout.strip()}")
            return True
    except:
        pass
    
    print("⚠ 未找到ChromeDriver，请确保已正确安装")
    return False


def run_pytest(test_paths=None, extra_args=None):
    """运行pytest测试"""
    platform_info = get_platform_info()
    
    # 基础命令
    cmd = [sys.executable, '-m', 'pytest']
    
    # 添加测试路径
    if test_paths:
        if isinstance(test_paths, str):
            test_paths = [test_paths]
        cmd.extend(test_paths)
    
    # 添加额外参数
    if extra_args:
        cmd.extend(extra_args)
    
    # 平台特定配置
    if platform_info['is_mac']:
        print("🍎 Mac平台执行模式")
        # Mac可能需要特殊处理
        env = os.environ.copy()
        env['PYTHONPATH'] = os.getcwd()
    else:
        env = None
    
    print(f"执行命令: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, env=env)
        return result.returncode
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"执行测试时出错: {e}")
        return 1


def main():
    parser = argparse.ArgumentParser(description='跨平台测试运行器')
    parser.add_argument('tests', nargs='*', 
                       help='测试文件或目录路径（支持多个）')
    parser.add_argument('-k', '--keyword', 
                       help='按关键字过滤测试')
    parser.add_argument('-m', '--marker', 
                       help='按标记过滤测试')
    parser.add_argument('--smoke', action='store_true',
                       help='只运行冒烟测试')
    parser.add_argument('--parallel', action='store_true',
                       help='并行执行测试')
    parser.add_argument('--html-report', 
                       help='生成HTML报告路径')
    parser.add_argument('--allure', action='store_true',
                       help='生成Allure报告')
    parser.add_argument('--check-env', action='store_true',
                       help='检查环境配置')
    
    args = parser.parse_args()
    
    # 检查环境
    if args.check_env:
        platform_info = get_platform_info()
        print(f"操作系统: {platform_info['system']}")
        print(f"Python版本: {sys.version}")
        check_chromedriver()
        return 0
    
    # 构建pytest参数
    pytest_args = []
    
    if args.keyword:
        pytest_args.extend(['-k', args.keyword])
    
    if args.marker:
        pytest_args.extend(['-m', args.marker])
    elif args.smoke:
        pytest_args.extend(['-m', 'smoke'])
    
    if args.parallel:
        pytest_args.extend(['-n', 'auto'])
    
    if args.html_report:
        pytest_args.extend(['--html', args.html_report])
    
    if args.allure:
        pytest_args.extend(['--alluredir', './allure-results'])
    
    # 确定测试路径
    test_paths = args.tests if args.tests else ['TestCases']
    
    # 验证测试路径
    valid_paths = []
    for path in test_paths:
        if os.path.exists(path):
            valid_paths.append(path)
        else:
            print(f"⚠ 路径不存在: {path}")
    
    if not valid_paths:
        print("❌ 没有找到有效的测试路径")
        return 1
    
    print(f"将执行以下测试: {', '.join(valid_paths)}")
    
    # 运行测试
    return run_pytest(valid_paths, pytest_args)


if __name__ == '__main__':
    sys.exit(main())
