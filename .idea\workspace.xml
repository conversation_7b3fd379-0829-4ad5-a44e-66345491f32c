<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="41bc7b94-36f3-4c95-bf68-c52cb3941ac2" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="1ua4LHkZ10twOZISBBM1dSit7yk" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python 测试.atest_pro_management_workorder.TesteWorkOrder.test_create_work_order 的 pytest.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (atest_pro_management_workorder.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (scrolllocs.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_base_data_equipment.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_creat_ticket.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_creat_unit.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_create_buliangping.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_customer.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_defects_product.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_knowledge_management.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_pro_management_reportwork.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_pro_management_workorder.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_process_routing.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_production_plan.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_scrolllocs.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_su_login.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.pytest (test_warehouse.py 内).executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test.test_example 的 pytest.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_creat_unit.TestCreateUnit.test_create_unit 的 pytest.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_defects_product.TestProductDefects 的 pytest.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_pro_management_workorder.TesteWorkOrder 的 pytest.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_pro_management_workorder.TesteWorkOrder.test_create_work_order 的 pytest.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_production_plan.TestProductionPlan 的 pytest.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_production_plan.TestProductionPlan.test_create_production_plan 的 pytest.executor&quot;: &quot;Run&quot;,
    &quot;Python 测试.test_su_login.TestLogin 的 pytest.executor&quot;: &quot;Debug&quot;,
    &quot;Python 测试.test_su_login.TestLogin.test_login_failed_no_user 的 pytest.executor&quot;: &quot;Debug&quot;,
    &quot;Python.conftest.executor&quot;: &quot;Run&quot;,
    &quot;Python.defective_products_locs.executor&quot;: &quot;Run&quot;,
    &quot;Python.hhettt.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.temp_001.executor&quot;: &quot;Run&quot;,
    &quot;Python.额.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/web/BlackLake/Conf&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.fileTypes&quot;
  }
}</component>
  <component name="PyDebuggerOptionsProvider">
    <option name="myPyQtBackend" value="PyQt5" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\web\BlackLake\Conf" />
      <recent name="D:\web\BlackLake\TestCases" />
      <recent name="D:\web\BlackLake\PageObjects" />
      <recent name="D:\web\BlackLake\PageLocators" />
      <recent name="D:\web_test\Exhibition\ATP" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\web\BlackLake\TestCases" />
      <recent name="D:\web\BlackLake\temp" />
      <recent name="D:\web_test\Exhibition\ATP" />
      <recent name="D:\web_test\Exhibition" />
      <recent name="D:\web_test\Exhibition\Outputsa" />
    </key>
  </component>
  <component name="RunManager" selected="Python.conftest">
    <configuration name="conftest" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Exhibition" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/TestCases" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/TestCases/conftest.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="Exhibition" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="atest_pro_management_workorder.TesteWorkOrder.test_create_work_order 的 pytest" type="tests" factoryName="py.test" temporary="true" nameIsGenerated="true">
      <module name="Exhibition" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/TestCases" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="_new_keywords" value="&quot;&quot;" />
      <option name="_new_parameters" value="&quot;&quot;" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;atest_pro_management_workorder.TesteWorkOrder.test_create_work_order&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="pytest (atest_pro_management_workorder.py 内)" type="tests" factoryName="py.test" temporary="true" nameIsGenerated="true">
      <module name="Exhibition" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/TestCases" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="_new_keywords" value="&quot;&quot;" />
      <option name="_new_parameters" value="&quot;&quot;" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;$PROJECT_DIR$/TestCases/atest_pro_management_workorder.py&quot;" />
      <option name="_new_targetType" value="&quot;PATH&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="pytest (test_production_plan.py 内)" type="tests" factoryName="py.test" temporary="true" nameIsGenerated="true">
      <module name="Exhibition" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/TestCases" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="_new_keywords" value="&quot;&quot;" />
      <option name="_new_parameters" value="&quot;&quot;" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;$PROJECT_DIR$/TestCases/test_production_plan.py&quot;" />
      <option name="_new_targetType" value="&quot;PATH&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="pytest (test.py 内)" type="tests" factoryName="py.test" temporary="true" nameIsGenerated="true">
      <module name="Exhibition" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/TestCases" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="_new_keywords" value="&quot;&quot;" />
      <option name="_new_parameters" value="&quot;&quot;" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;$PROJECT_DIR$/TestCases/test.py&quot;" />
      <option name="_new_targetType" value="&quot;PATH&quot;" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.main" />
      <item itemvalue="Python.conftest" />
      <item itemvalue="Python 测试.atest_pro_management_workorder.TesteWorkOrder.test_create_work_order 的 pytest" />
      <item itemvalue="Python 测试.pytest (atest_pro_management_workorder.py 内)" />
      <item itemvalue="Python 测试.pytest (test_production_plan.py 内)" />
      <item itemvalue="Python 测试.pytest (test.py 内)" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.conftest" />
        <item itemvalue="Python 测试.pytest (test.py 内)" />
        <item itemvalue="Python 测试.pytest (atest_pro_management_workorder.py 内)" />
        <item itemvalue="Python 测试.atest_pro_management_workorder.TesteWorkOrder.test_create_work_order 的 pytest" />
        <item itemvalue="Python 测试.pytest (test_production_plan.py 内)" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="41bc7b94-36f3-4c95-bf68-c52cb3941ac2" name="Default Changelist" comment="" />
      <created>1624890308721</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1624890308721</updated>
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/temp/创建单位.py</url>
          <line>18</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>