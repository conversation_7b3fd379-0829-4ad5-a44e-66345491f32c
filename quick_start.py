#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端页面快速启动脚本
自动打开浏览器并登录黑湖生产管理系统
"""

import sys
import time
import webbrowser
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 系统配置
LOGIN_URL = "https://liteweb-test.blacklake.cn/"
FACTORY_CODE = "990993"
USERNAME = "admin"
PASSWORD = "Blacklake123"

def open_browser_only():
    """仅打开浏览器到登录页面"""
    print("🌐 正在打开前端页面...")
    webbrowser.open(LOGIN_URL)
    print("✅ 前端页面已在浏览器中打开！")
    print(f"🔗 页面地址: {LOGIN_URL}")
    print("🔑 登录信息:")
    print(f"   工厂代码: {FACTORY_CODE}")
    print(f"   账号: {USERNAME}")
    print(f"   密码: {PASSWORD}")
    print("\n请手动完成登录操作。")

def auto_login():
    """自动登录到系统"""
    print("🚀 正在启动自动登录...")
    
    # Chrome选项配置
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    try:
        # 启动浏览器
        print("📱 正在启动Chrome浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # 访问登录页面
        print(f"🌐 正在访问: {LOGIN_URL}")
        driver.get(LOGIN_URL)
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        
        # 选择工厂账号登录标签
        print("🏭 选择工厂账号登录...")
        factory_tab = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//*[@id='rc-tabs-0-tab-1']"))
        )
        factory_tab.click()
        time.sleep(1)
        
        # 输入工厂代码
        print(f"🏢 输入工厂代码: {FACTORY_CODE}")
        factory_input = wait.until(
            EC.presence_of_element_located((By.XPATH, '//input[@placeholder="请输入工厂代码"]'))
        )
        factory_input.clear()
        factory_input.send_keys(FACTORY_CODE)
        time.sleep(0.5)
        
        # 输入用户名
        print(f"👤 输入账号: {USERNAME}")
        username_input = driver.find_element(By.XPATH, '//input[@placeholder="请输入账号"]')
        username_input.clear()
        username_input.send_keys(USERNAME)
        time.sleep(0.5)
        
        # 输入密码
        print("🔐 输入密码...")
        password_input = driver.find_element(
            By.XPATH, 
            '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[3]/div[1]/div/div[1]/div/span/input'
        )
        password_input.clear()
        password_input.send_keys(PASSWORD)
        time.sleep(0.5)
        
        # 点击登录按钮
        print("🚪 点击登录按钮...")
        login_button = driver.find_element(
            By.XPATH, 
            '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[4]/div/div/div/div/button'
        )
        login_button.click()
        
        # 等待登录完成
        print("⏳ 等待登录完成...")
        time.sleep(3)
        
        # 检查是否有跳过按钮（可能出现的引导页面）
        try:
            skip_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/form/div[3]/div/div/div/div/button[1]/span'))
            )
            print("⏭ 点击跳过按钮...")
            skip_button.click()
            time.sleep(2)
        except:
            print("ℹ️ 未发现跳过按钮，继续...")
        
        print("✅ 登录成功！")
        print("🎉 前端页面已启动并自动登录完成！")
        print("🖥️ 浏览器将保持打开状态，您可以开始使用系统。")
        
        # 保持浏览器打开
        input("\n按回车键关闭浏览器...")
        
    except Exception as e:
        print(f"❌ 自动登录失败: {str(e)}")
        print("💡 建议使用手动登录方式")
        return False
    finally:
        try:
            driver.quit()
        except:
            pass
    
    return True

def show_help():
    """显示帮助信息"""
    print("🔧 前端页面启动脚本")
    print("=" * 50)
    print("使用方法:")
    print("  python quick_start.py [选项]")
    print("")
    print("选项:")
    print("  auto    - 自动登录（推荐）")
    print("  open    - 仅打开浏览器")
    print("  help    - 显示此帮助信息")
    print("")
    print("示例:")
    print("  python quick_start.py auto   # 自动登录")
    print("  python quick_start.py open   # 仅打开浏览器")
    print("")
    print("系统信息:")
    print(f"  页面地址: {LOGIN_URL}")
    print(f"  工厂代码: {FACTORY_CODE}")
    print(f"  账号: {USERNAME}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        # 默认使用自动登录
        mode = "auto"
    else:
        mode = sys.argv[1].lower()
    
    if mode == "help" or mode == "-h" or mode == "--help":
        show_help()
    elif mode == "open":
        open_browser_only()
    elif mode == "auto":
        success = auto_login()
        if not success:
            print("\n🔄 切换到手动登录模式...")
            open_browser_only()
    else:
        print(f"❌ 未知选项: {mode}")
        print("💡 使用 'python quick_start.py help' 查看帮助")

if __name__ == "__main__":
    main()
