[tool:pytest]
# pytest配置文件 - 跨平台兼容

# 测试发现
testpaths = TestCases
python_files = test_*.py *_test.py atest_*.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --alluredir=./allure-results
    --html=./Outputs/reports/report.html
    --self-contained-html

# 标记定义
markers =
    smoke: 冒烟测试
    regression: 回归测试
    slow: 慢速测试
    mac: Mac平台特定测试
    windows: Windows平台特定测试
    linux: Linux平台特定测试

# 最小版本要求
minversion = 6.0

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 并行执行配置（可选，需要安装pytest-xdist）
# addopts = -n auto

# 超时配置（可选，需要安装pytest-timeout）
# timeout = 300
