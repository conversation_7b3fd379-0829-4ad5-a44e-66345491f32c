@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    黑湖生产管理系统 - 前端启动工具
echo ========================================
echo.

echo 🌐 系统信息:
echo    页面地址: https://liteweb-test.blacklake.cn/
echo    工厂代码: 990993
echo    账号: admin
echo    密码: Blacklake123
echo.

echo 请选择启动方式:
echo [1] 自动登录 (推荐)
echo [2] 仅打开浏览器
echo [3] 运行登录测试
echo [4] 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto auto_login
if "%choice%"=="2" goto open_browser
if "%choice%"=="3" goto run_test
if "%choice%"=="4" goto exit
goto invalid_choice

:auto_login
echo.
echo 🚀 正在启动自动登录...
python quick_start.py auto
goto end

:open_browser
echo.
echo 🌐 正在打开浏览器...
python quick_start.py open
goto end

:run_test
echo.
echo 🧪 正在运行登录测试...
if exist venv\Scripts\activate.bat (
    call venv\Scripts\activate.bat
    echo ✅ 虚拟环境已激活
)
python run_tests.py TestCases/test_su_login.py
goto end

:invalid_choice
echo.
echo ❌ 无效选择，请输入 1-4
pause
goto start

:end
echo.
echo 操作完成！
pause
goto exit

:exit
exit
