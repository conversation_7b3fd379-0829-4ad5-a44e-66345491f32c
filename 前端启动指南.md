# 前端页面启动指南

## 🌐 系统信息

### 前端页面地址
```
https://liteweb-test.blacklake.cn/
```

### 登录信息
- **工厂代码**: 990993
- **账号**: admin  
- **密码**: Blacklake123

## 🚀 启动方式

### 方式一：直接访问（推荐）
1. 打开浏览器（推荐Chrome）
2. 访问：`https://liteweb-test.blacklake.cn/`
3. 选择"工厂账号登录"标签
4. 输入登录信息：
   - 工厂代码：990993
   - 账号：admin
   - 密码：Blacklake123
5. 点击登录

### 方式二：通过自动化测试启动
```bash
# 1. 激活虚拟环境（如果使用虚拟环境）
source venv/bin/activate  # Mac/Linux
# 或
venv\Scripts\activate     # Windows

# 2. 运行登录测试（会自动打开浏览器并登录）
python run_tests.py TestCases/test_su_login.py

# 3. 或者运行单个登录测试
pytest TestCases/test_su_login.py -v
```

### 方式三：使用临时脚本启动
```python
# 创建临时启动脚本 quick_start.py
from selenium import webdriver
from selenium.webdriver.common.by import By
import time

# 启动浏览器
driver = webdriver.Chrome()
driver.maximize_window()

# 访问系统
driver.get("https://liteweb-test.blacklake.cn/")
time.sleep(3)

# 选择工厂账号登录
driver.find_element(By.XPATH, "//*[@id='rc-tabs-0-tab-1']").click()

# 输入登录信息
driver.find_element(By.XPATH, '//input[@placeholder="请输入工厂代码"]').send_keys("990993")
driver.find_element(By.XPATH, '//input[@placeholder="请输入账号"]').send_keys("admin")
driver.find_element(By.XPATH, '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[3]/div[1]/div/div[1]/div/span/input').send_keys("Blacklake123")

# 点击登录
driver.find_element(By.XPATH, '/html/body/div[1]/div/div[2]/div[2]/div[2]/div[2]/div/div[2]/form/div[4]/div/div/div/div/button').click()

print("✅ 前端页面已启动并登录成功！")
print("🌐 页面地址: https://liteweb-test.blacklake.cn/")

# 保持浏览器打开
input("按回车键关闭浏览器...")
driver.quit()
```

## 📋 系统功能模块

登录成功后，您可以访问以下功能模块：

### 主要菜单
- **主页** - 系统仪表盘
- **基础数据** - 基础信息管理
  - 客户管理
  - 设备管理  
  - 单位管理
  - 仓库管理
  - 不良品项管理
  - 供应商管理
- **生产管理** - 生产流程管理
  - 工单管理
  - 生产计划
  - 报工管理
  - 工艺路线

## 🔧 环境要求

### 浏览器要求
- **推荐**: Google Chrome（最新版本）
- **支持**: Firefox, Safari, Edge
- **注意**: 确保浏览器支持现代Web标准

### 网络要求
- 稳定的互联网连接
- 能够访问 `blacklake.cn` 域名
- 建议使用有线网络以确保稳定性

## 🛠 故障排除

### 常见问题

#### 1. 无法访问页面
```bash
# 检查网络连接
ping blacklake.cn

# 检查DNS解析
nslookup liteweb-test.blacklake.cn
```

#### 2. 登录失败
- 确认工厂代码：990993
- 确认账号：admin
- 确认密码：Blacklake123
- 检查是否选择了"工厂账号登录"标签

#### 3. 页面加载缓慢
- 清除浏览器缓存
- 尝试使用无痕/隐私模式
- 检查网络连接速度

#### 4. 浏览器兼容性问题
```bash
# 更新Chrome浏览器
# Windows: 通过Chrome菜单更新
# Mac: brew update && brew upgrade google-chrome
# Linux: sudo apt update && sudo apt upgrade google-chrome-stable
```

## 📱 移动端访问

系统支持移动端访问：
- 在手机浏览器中访问相同地址
- 使用相同的登录信息
- 界面会自动适配移动设备

## 🔐 安全注意事项

1. **测试环境**: 这是测试环境，请勿用于生产数据
2. **账号安全**: 请勿在公共网络下使用
3. **数据保护**: 测试数据可能会被定期清理
4. **访问控制**: 仅限授权人员使用

## 📞 技术支持

如果遇到问题，请：
1. 检查本指南的故障排除部分
2. 查看浏览器控制台错误信息
3. 联系系统管理员或技术支持团队

---

**快速启动命令**:
```bash
# 一键启动（推荐）
python -c "
import webbrowser
webbrowser.open('https://liteweb-test.blacklake.cn/')
print('✅ 前端页面已在浏览器中打开！')
print('🔑 登录信息: 990993 / admin / Blacklake123')
"
```
