#!/bin/bash
# 黑湖生产管理系统 - 前端启动工具

echo ""
echo "========================================"
echo "   黑湖生产管理系统 - 前端启动工具"
echo "========================================"
echo ""

echo "🌐 系统信息:"
echo "   页面地址: https://liteweb-test.blacklake.cn/"
echo "   工厂代码: 990993"
echo "   账号: admin"
echo "   密码: Blacklake123"
echo ""

echo "请选择启动方式:"
echo "[1] 自动登录 (推荐)"
echo "[2] 仅打开浏览器"
echo "[3] 运行登录测试"
echo "[4] 退出"
echo ""

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🚀 正在启动自动登录..."
        python3 quick_start.py auto
        ;;
    2)
        echo ""
        echo "🌐 正在打开浏览器..."
        python3 quick_start.py open
        ;;
    3)
        echo ""
        echo "🧪 正在运行登录测试..."
        if [ -f "venv/bin/activate" ]; then
            source venv/bin/activate
            echo "✅ 虚拟环境已激活"
        fi
        python3 run_tests.py TestCases/test_su_login.py
        ;;
    4)
        echo "👋 再见！"
        exit 0
        ;;
    *)
        echo ""
        echo "❌ 无效选择，请输入 1-4"
        exit 1
        ;;
esac

echo ""
echo "操作完成！"
